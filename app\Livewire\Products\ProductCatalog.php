<?php

namespace App\Livewire\Products;

use Livewire\Component;
use Livewire\WithPagination;
use LBCDev\Ecommerce\Models\Product;

class ProductCatalog extends Component
{
    use WithPagination;

    public $search = '';
    public $sortBy = 'name';
    public $sortDirection = 'asc';
    public $perPage = 12;
    public $selectedCategories = [];
    public $selectedTags = [];

    public $isProductsLoading = false;

    protected $listeners = [
        'searchUpdated' => 'updateSearch',
        'sortUpdated' => 'updateSort',
        'filtersUpdated' => 'updateFilters',
        'addToCartFromCard' => 'handleAddToCart',
        'products-loading-start' => 'startLoading',
        'products-loading-end' => 'endLoading',
    ];

    protected $queryString = [
        'search' => ['except' => ''],
        'sortBy' => ['except' => 'name'],
        'sortDirection' => ['except' => 'asc'],
        'selectedCategories' => ['except' => []],
        'selectedTags' => ['except' => []],
    ];

    public function startLoading()
    {
        $this->isProductsLoading = true;
    }

    public function endLoading()
    {
        $this->isProductsLoading = false;
    }

    public function updateSearch($searchTerm)
    {
        $this->search = $searchTerm;
        $this->resetPage();
    }

    public function updateSort($field, $direction)
    {
        $this->sortBy = $field;
        $this->sortDirection = $direction;
        $this->resetPage();
    }

    public function updateFilters($categories, $tags)
    {
        $this->selectedCategories = $categories;
        $this->selectedTags = $tags;
        $this->resetPage();
    }

    public function handleAddToCart($productId, $quantity = 1)
    {
        $this->dispatch('addToCart', productId: $productId, quantity: $quantity);
        session()->flash('success', 'Producto agregado al carrito.');
    }

    public function getProductsProperty()
    {
        return Product::query()
            ->where('is_active', true)
            ->when($this->search, function ($query) {
                $query->where(function ($q) {
                    $q->where('name', 'like', '%' . $this->search . '%')
                        ->orWhere('short_description', 'like', '%' . $this->search . '%')
                        ->orWhere('description', 'like', '%' . $this->search . '%');
                });
            })
            ->when(!empty($this->selectedCategories), function ($query) {
                $query->whereHas('categoriesRelation', function ($q) {
                    $q->whereIn('taxonomy_id', $this->selectedCategories);
                });
            })
            ->when(!empty($this->selectedTags), function ($query) {
                $query->whereHas('tagsRelation', function ($q) {
                    $q->whereIn('taxonomy_id', $this->selectedTags);
                });
            })
            ->orderBy($this->sortBy, $this->sortDirection)
            ->paginate($this->perPage);
    }

    public function render()
    {
        return view('livewire.products.product-catalog', [
            'products' => $this->products,
        ]);
    }
}
