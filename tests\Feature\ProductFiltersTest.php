<?php

namespace Tests\Feature;

use Tests\TestCase;
use Livewire\Livewire;
use LBCDev\Ecommerce\Models\Tag;
use LBCDev\Ecommerce\Models\Product;
use LBCDev\Ecommerce\Models\Category;
use App\Livewire\Products\ProductFilters;
use App\Livewire\Products\ProductCatalog;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ProductFiltersTest extends TestCase
{
    use RefreshDatabase;

    protected $category;
    protected $subcategory;
    protected $tag;
    protected $product;

    protected function setUp(): void
    {
        parent::setUp();

        // Crear categoría raíz
        $root = Category::create([
            'name' => 'Categorías',
            'slug' => config('lbcdev-ecommerce.categories_taxonomy_type'),
            'type' => config('lbcdev-ecommerce.categories_taxonomy_type'),
        ]);

        // Crear categoría principal
        $this->category = Category::create([
            'name' => 'Electrónicos',
            'slug' => 'electronicos',
            'type' => config('lbcdev-ecommerce.categories_taxonomy_type'),
            'parent_id' => $root->id,
        ]);

        // Crear subcategoría
        $this->subcategory = Category::create([
            'name' => 'Smartphones',
            'slug' => 'smartphones',
            'type' => config('lbcdev-ecommerce.categories_taxonomy_type'),
            'parent_id' => $this->category->id,
        ]);

        // Crear tag
        $this->tag = Tag::create([
            'name' => 'Oferta',
            'slug' => 'oferta',
            'type' => config('lbcdev-ecommerce.tags_taxonomy_type'),
        ]);

        // Crear producto
        $this->product = Product::factory()->create([
            'name' => 'iPhone 15',
            'is_active' => true,
        ]);

        // Asociar categoría y tag al producto
        $this->product->syncCategories([$this->category->id]);
        $this->product->syncTags([$this->tag->id]);
    }

    public function test_product_filters_component_renders()
    {
        Livewire::test(ProductFilters::class)
            ->assertStatus(200)
            ->assertSee('Filtros')
            ->assertSee('Categorías')
            ->assertSee('Etiquetas');
    }

    public function test_product_filters_shows_categories_and_tags()
    {
        Livewire::test(ProductFilters::class)
            ->assertSee($this->category->name)
            ->assertSee($this->subcategory->name)
            ->assertSee($this->tag->name);
    }

    public function test_can_toggle_category_filter()
    {
        Livewire::test(ProductFilters::class)
            ->call('toggleCategory', $this->category->id)
            ->assertSet('selectedCategories', [$this->category->id])
            ->assertDispatched('filtersUpdated');
    }

    public function test_can_toggle_tag_filter()
    {
        Livewire::test(ProductFilters::class)
            ->call('toggleTag', $this->tag->id)
            ->assertSet('selectedTags', [$this->tag->id])
            ->assertDispatched('filtersUpdated');
    }

    public function test_can_clear_filters()
    {
        Livewire::test(ProductFilters::class)
            ->set('selectedCategories', [$this->category->id])
            ->set('selectedTags', [$this->tag->id])
            ->call('clearFilters')
            ->assertSet('selectedCategories', [])
            ->assertSet('selectedTags', [])
            ->assertDispatched('filtersUpdated');
    }

    public function test_product_catalog_filters_products_by_category()
    {
        // Crear otro producto sin la categoría
        $otherProduct = Product::factory()->create([
            'name' => 'Producto sin categoría',
            'is_active' => true,
        ]);

        Livewire::test(ProductCatalog::class)
            ->set('selectedCategories', [$this->category->id])
            ->assertSee($this->product->name)
            ->assertDontSee($otherProduct->name);
    }

    public function test_product_catalog_filters_products_by_tag()
    {
        // Crear otro producto sin el tag
        $otherProduct = Product::factory()->create([
            'name' => 'Producto sin tag',
            'is_active' => true,
        ]);

        Livewire::test(ProductCatalog::class)
            ->set('selectedTags', [$this->tag->id])
            ->assertSee($this->product->name)
            ->assertDontSee($otherProduct->name);
    }

    public function test_product_catalog_updates_filters_from_event()
    {
        Livewire::test(ProductCatalog::class)
            ->call('updateFilters', [$this->category->id], [$this->tag->id])
            ->assertSet('selectedCategories', [$this->category->id])
            ->assertSet('selectedTags', [$this->tag->id]);
    }
}
