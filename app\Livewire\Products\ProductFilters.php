<?php

namespace App\Livewire\Products;

use Livewire\Component;
use LBCDev\Ecommerce\Models\Category;
use LBCDev\Ecommerce\Models\Tag;

class ProductFilters extends Component
{
    public $selectedCategories = [];
    public $selectedTags = [];

    public function mount($initialCategories = [], $initialTags = [])
    {
        $this->selectedCategories = $initialCategories;
        $this->selectedTags = $initialTags;
    }

    public function updatedSelectedCategories()
    {
        $this->dispatch('products-loading-start');
        $this->dispatch('filtersUpdated', $this->selectedCategories, $this->selectedTags);
        $this->dispatch('products-loading-end');
    }

    public function updatedSelectedTags()
    {
        $this->dispatch('products-loading-start');
        $this->dispatch('filtersUpdated', $this->selectedCategories, $this->selectedTags);
        $this->dispatch('products-loading-end');
    }

    public function clearFilters()
    {
        $this->dispatch('products-loading-start');
        $this->selectedCategories = [];
        $this->selectedTags = [];
        $this->dispatch('filtersUpdated', [], []);
        $this->dispatch('products-loading-end');
    }

    public function toggleCategory($categoryId)
    {
        $this->dispatch('products-loading-start');
        
        if (in_array($categoryId, $this->selectedCategories)) {
            $this->selectedCategories = array_values(array_diff($this->selectedCategories, [$categoryId]));
        } else {
            $this->selectedCategories[] = $categoryId;
        }
        
        $this->dispatch('filtersUpdated', $this->selectedCategories, $this->selectedTags);
        $this->dispatch('products-loading-end');
    }

    public function toggleTag($tagId)
    {
        $this->dispatch('products-loading-start');
        
        if (in_array($tagId, $this->selectedTags)) {
            $this->selectedTags = array_values(array_diff($this->selectedTags, [$tagId]));
        } else {
            $this->selectedTags[] = $tagId;
        }
        
        $this->dispatch('filtersUpdated', $this->selectedCategories, $this->selectedTags);
        $this->dispatch('products-loading-end');
    }

    public function getCategoriesProperty()
    {
        return Category::where('parent_id', '!=', Category::root()->id)
            ->with('children')
            ->whereNull('parent_id')
            ->orWhere('parent_id', Category::root()->id)
            ->orderBy('name')
            ->get();
    }

    public function getTagsProperty()
    {
        return Tag::orderBy('name')->get();
    }

    public function render()
    {
        return view('livewire.products.product-filters', [
            'categories' => $this->categories,
            'tags' => $this->tags,
        ]);
    }
}
