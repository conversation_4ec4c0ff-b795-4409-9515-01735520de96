<?php

namespace App\Livewire\Products;

use Livewire\Component;
use LBCDev\Ecommerce\Models\Product;

class ProductCard extends Component
{
    public $productId;
    public $name;
    public $shortDescription;
    public $price;

    public function mount(Product $product)
    {
        $this->productId = $product->id;
        $this->name = $product->name;
        $this->shortDescription = $product->short_description;
        $this->price = $product->price;
    }

    public function addToCart($quantity = 1)
    {
        $this->dispatch('addToCartFromCard', $this->productId, $quantity);
    }

    public function render()
    {
        return view('livewire.products.product-card');
    }
}
