<div>
    <!-- Loading Indicator for general actions -->
    <livewire:ui.loading-indicator 
        message="Cargando productos..." 
        type="bar" 
        color="blue" />

    <!-- Search and Sort Bar -->
    <div class="mb-6 bg-white p-4 rounded-lg shadow">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <!-- Search Component -->
            <div class="flex-1 max-w-md">
                <livewire:products.product-search :initial-search="$search" />
            </div>

            <!-- Sort Component -->
            <div>
                <livewire:products.product-sort
                    :initial-sort-by="$sortBy"
                    :initial-direction="$sortDirection" />
            </div>
        </div>
    </div>

    <!-- Main Content Area with Filters and Products -->
    <div class="flex flex-col lg:flex-row gap-6">
        <!-- Filters Sidebar -->
        <div class="lg:w-64 flex-shrink-0">
            <livewire:products.product-filters
                :initial-categories="$selectedCategories"
                :initial-tags="$selectedTags" />
        </div>

        <!-- Products Area -->
        <div class="flex-1">

    <!-- Products Grid -->
    <div class="relative" 
        x-data="{ isLoading: false }"
        @products-loading-start.window="isLoading = true"
        @products-loading-end.window="setTimeout(() => isLoading = false, 1000)">
        <!-- Loading overlay for products grid -->
        <div x-show="isLoading" 
            x-transition
            class="absolute inset-0 bg-white bg-opacity-50 z-10 flex items-top justify-center">

            <div class="flex items-top justify-center space-x-2 py-4">
                <svg class="animate-spin h-8 w-8 text-blue-600" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span class="text-sm text-blue-600">Actualizando productos...</span>
            </div>
        </div>
        
        @if($products->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
                @foreach($products as $product)
                    <livewire:products.product-card :product="$product" :key="$product->id" />
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="mt-6">
                {{ $products->links() }}
            </div>
        @else
            <!-- No Products Found -->
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2-2v7m14 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m14 0H6m14 0l-3-3m-3 3l-3-3"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No se encontraron productos</h3>
                <p class="mt-1 text-sm text-gray-500">
                    @if($search)
                        No hay productos que coincidan con "{{ $search }}"
                    @else
                        No hay productos disponibles en este momento.
                    @endif
                </p>
            </div>
        @endif
        </div>
    </div>

    <!-- Flash Messages -->
    @if (session()->has('success'))
        <div x-data="{ show: true }" x-show="show" x-transition x-init="setTimeout(() => show = false, 3000)"
             class="fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50">
            {{ session('success') }}
        </div>
    @endif
</div>